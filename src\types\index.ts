export interface Product {
  id: string;
  name: string;
  description: string;
  price: number;
  category: string;
  rating: number;
  image_url: string;
  availability: boolean;
  stock: number;
  brand?: string;
  specs?: Record<string, any>;
}

export interface User {
  id: string;
  email: string;
  name: string;
  avatar?: string;
}

export interface ChatMessage {
  id: string;
  content: string;
  sender: 'user' | 'bot';
  timestamp: Date;
  products?: Product[];
  type?: 'text' | 'products' | 'filter_suggestion';
}

export interface CartItem {
  product: Product;
  quantity: number;
}

export interface Filters {
  category: string;
  priceRange: [number, number];
  minRating: number;
  availability: boolean;
  brand?: string;
}

export interface ChatSession {
  id: string;
  messages: ChatMessage[];
  createdAt: Date;
  updatedAt: Date;
}