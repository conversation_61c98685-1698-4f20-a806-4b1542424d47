import React from 'react';
import { motion } from 'framer-motion';
import { Bo<PERSON> } from 'lucide-react';

export const TypingIndicator: React.FC = () => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      className="flex items-start space-x-3 mb-4"
    >
      {/* Bot Avatar */}
      <div className="w-8 h-8 rounded-full bg-gradient-to-r from-blue-500 to-purple-500 flex items-center justify-center">
        <Bot className="w-4 h-4 text-white" />
      </div>
      
      {/* Typing Animation */}
      <div className="bg-gray-100 dark:bg-gray-700 rounded-2xl p-3 flex items-center space-x-1">
        <div className="flex space-x-1">
          {[0, 1, 2].map((i) => (
            <motion.div
              key={i}
              className="w-2 h-2 bg-gray-400 dark:bg-gray-500 rounded-full"
              animate={{ scale: [1, 1.2, 1] }}
              transition={{
                duration: 0.8,
                repeat: Infinity,
                delay: i * 0.2
              }}
            />
          ))}
        </div>
        <span className="text-sm text-gray-500 dark:text-gray-400 ml-2">
          AI is thinking...
        </span>
      </div>
    </motion.div>
  );
};